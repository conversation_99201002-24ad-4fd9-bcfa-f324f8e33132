# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON>i
<PERSON>reate Time: 2025/8/12
File Name: draft_tools.py
草稿相关的MCP工具函数
"""
import uuid
import json
import os
from jianyingdraft.config import SAVE_PATH
from jianyingdraft.utils.response import ToolResponse
from mcp.server.fastmcp import FastMCP


def draft_tool(mcp: FastMCP):
    @mcp.tool
    def create_draft(draft_name: str = '', width: int = 1920, height: int = 1080, fps: int = 30) -> ToolResponse:
        """
        创建草稿

        Args:
            draft_name: str 草稿名称，默认为空，不过最好加上
            width: int 视频宽度，默认1920
            height: int 视频高度，默认1080
            fps: int 帧率，默认30
        """

        # 验证SAVE_PATH是否存在
        if not os.path.exists(SAVE_PATH):
            return ToolResponse(success=False, message=f"草稿存储路径不存在: {SAVE_PATH}")

        # 生成草稿ID
        draft_id = str(uuid.uuid4())
        # 构建完整的草稿路径
        draft_path = os.path.join(SAVE_PATH, draft_id)

        # 创建草稿数据
        draft_data = {
            "draft_id": draft_id,
            "draft_name": draft_name,
            "width": width,
            "height": height,
            "fps": fps
        }
        os.makedirs(draft_path, exist_ok=True)
        draft_json_path = os.path.join(draft_path, "draft.json")
        with open(draft_json_path, "w", encoding="utf-8") as f:
            json.dump(draft_data, f, ensure_ascii=False, indent=4)

        response = ToolResponse(success=True, message="草稿创建成功", data=draft_data)
        return response
